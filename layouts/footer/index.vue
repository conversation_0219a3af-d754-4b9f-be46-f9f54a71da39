<script setup lang="ts">
import { Icon } from "@iconify/vue";
const FooterLinks = ref([
  {
    title: "Shop",
    url: "/shop",
  },
  {
    title: "Collections",
    url: "/collections",
  },
  {
    title: "Our Artisans",
    url: "/artisans",
  },
  {
    title: "About Nora",
    url: "/about",
  },
  {
    title: "My Account",
    url: "/account",
  },
  {
    title: "Terms",
    url: "/terms-conditions",
  },
  {
    title: "Privacy Policy",
    url: "/privacy-policy",
  },
]);

const SocialLinks = ref([
  {
    title: "Facebook",
    url: "https://www.facebook.com/",
  },
  {
    title: "Instagram",
    url: "https://www.instagram.com/",
  },
  {
    title: "Twitter",
    url: "https://x.com/",
  },
  
]);
</script>
<template>
  <div class="bg-secondary">
    <SharedSectionSpacer />
    <div class="container-lg">
      <v-row>
        <v-col cols="12" lg="5">
          <div class="d-flex flex-column ga-10">
            <h2 class="text-h2 pe-lg-16">Discover Authentic Ghanaian Fashion</h2>
            <p class="text-subtitle-1 text-white pe-lg-8">
              Shop unique, handcrafted fashion pieces created by talented artisans from Northern Ghana. Every purchase supports local creators and celebrates authentic African craftsmanship.
            </p>
            <div>
              <div class="d-flex flex-column ga-2">
                <NuxtLink
                  href="mailto:<EMAIL>"
                  target="_blank"
                  class="d-flex ga-3 align-center text-white text-decoration-none hover-primary-link"
                >
                  <Icon
                    icon="material-symbols:arrow-outward"
                    height="24"
                    class="text-primary"
                  />
                  <EMAIL></NuxtLink
                >
                <NuxtLink
                  href="#"
                  target="_blank"
                  class="d-flex ga-3 align-center text-white text-decoration-none hover-primary-link"
                >
                  <Icon
                    icon="uil:map-marker"
                    height="24"
                    class="text-primary"
                  />Northern Ghana</NuxtLink
                >
              </div>
            </div>
          </div>
        </v-col>
        <v-col cols="12" lg="2" md="4">
          <ul class="list-style-none d-flex flex-column ga-2">
            <li v-for="(item, i) in FooterLinks" :key="i" class="">
              <NuxtLink :to="item.url" class="text-subtitle-1 text-white text-decoration-none hover-primary-link ">{{ item.title }}</NuxtLink>
            </li>
          </ul>
        </v-col>
        <v-col cols="12" lg="2" md="4">
            <ul class="list-style-none d-flex flex-column ga-2">
            <li v-for="(item, i) in SocialLinks" :key="i" class="">
              <NuxtLink :to="item.url" target="_blank" class="text-subtitle-1 text-white text-decoration-none hover-primary-link ">{{ item.title }}</NuxtLink>
            </li>
          </ul>
        </v-col>
        <v-col cols="12" lg="3" md="4">
            <p class="text-subtitle-1 text-white text-lg-right text-left">© Nora copyright 2025</p>
        </v-col>
      </v-row>
    </div>
    <SharedSectionSpacer />

    <ScrollToTop/>
  </div>
</template>
