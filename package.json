{"name": "<PERSON>-nuxtjs", "version": "1.0.0", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@iconify/vue": "^5.0.0", "@mdi/font": "^7.4.47", "@pinia/nuxt": "^0.11.1", "axios": "^1.10.0", "axios-mock-adapter": "^2.1.0", "chance": "^1.1.13", "date-fns": "^4.1.0", "lodash": "^4.17.21", "nuxt": "^3.17.5", "pinia": "^3.0.3", "sass": "^1.89.2", "vite-plugin-vuetify": "^2.1.1", "vue": "^3.5.16", "vue-router": "^4.5.1", "vue3-carousel": "^0.16.0", "vuetify": "^3.8.9"}, "devDependencies": {"@types/chance": "^1.1.6", "@types/lodash": "^4.17.18"}}