<script setup lang="ts">
import AnimatedIcon from "/images/svgs/astrisk-icon.svg";
</script>
<template>
  <v-container fluid class="hero-container pa-0">
    <!-- Video Background -->
    <video autoplay muted loop playsinline class="video-bg">
      <source src="/video/banner-video.mp4" type="video/mp4" />
    </video>

    <!-- Dark overlay -->
    <div class="overlay"></div>

    <!-- Content bottom-left -->
    <div class="container-lg">
      <div class="hero-content">
        <div class="mw-460">
          <!-- Top row: icon + paragraph -->
          <div class="d-flex ga-6">
            <img
              :src="AnimatedIcon"
              alt="icon"
              height="44"
              width="44"
              class="icon-rotate"
            />
            <p class="text-white text-subtitle-1 mb-0">
              <span class="opacity-70">We showcase</span>
              <span class="text-primary opacity-100">
                authentic fashion
              </span>
              <span class="opacity-70"
                >crafted by talented artisans from Northern Ghana, celebrating heritage through style.</span
              >
            </p>
          </div>
        </div>

        <!-- Bottom row: title + badge -->
        <div class="d-flex flex-md-row flex-column ga-5 align-md-end align-start mt-md-6 mt-3">
          <h1 class="text-white text-h1 font-weight-bold mb-0">Heritage</h1>
          <SharedBigBadge />
        </div>

        <!-- Call to action -->
        <div class="mt-8">
          <v-btn
            to="/shop"
            size="large"
            color="primary"
            class="rounded-pill px-8 py-3 text-h6 font-weight-bold"
            elevation="0"
          >
            Shop Now
          </v-btn>
          <v-btn
            to="/artisans"
            size="large"
            variant="outlined"
            color="white"
            class="rounded-pill px-8 py-3 text-h6 font-weight-bold ml-4"
            elevation="0"
          >
            Meet Our Artisans
          </v-btn>
        </div>
      </div>
    </div>
  </v-container>
</template>
