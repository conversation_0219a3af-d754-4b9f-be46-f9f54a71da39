.header {
   position: fixed;
    top: 0;
    width: 100%;
    z-index: 999;
    padding: 28px 0;
    transition: padding 0.5s ease-in-out, background-color 0.5s ease-in-out;
    border-top: 4px solid rgb(var(--v-theme-primary));
    border-bottom: 0;
}

.sticky-header {
    padding: 20px 0;
    border-top-color: transparent;
}

.menu-list {
    list-style: none;

    li {
        a {
            img {
                transition: 0.5s;
            }

            text-decoration: none;
            color: rgb(var(--v-theme-secondary));

            .hide-icon {
                opacity: 0;
                width: 0;
            }

            &:hover {
                .hide-icon {
                    opacity: 1;
                    width: 20px;
                    margin-inline-end: 10px;
                }
            }

            &.router-link-active {
                .hide-icon {
                    opacity: 1;
                    width: 20px;
                    margin-inline-end: 10px;
                }
            }
        }
    }
}

@media screen and (max-width:767px) {
    .header {
        padding: 15px 0;
    }

    .sticky-header {
        padding: 12px 0;
    }
}