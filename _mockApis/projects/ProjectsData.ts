import { Chance } from "chance";
import { random } from "lodash";
import { sub } from "date-fns";
import mock from "../mockAdapter";

// Main Images
import project1 from "/images/projects/snapclear.webp";
import project2 from "/images/projects/project1.webp";
import project3 from "/images/projects/project2.webp";
import project4 from "/images/projects/project3.webp";
import project5 from "/images/projects/project4.webp";
import project6 from "/images/projects/project5.webp";

// Detail images
import project11 from "/images/projects/details/pd-11.webp";
import project12 from "/images/projects/details/pd-12.webp";
import project13 from "/images/projects/details/pd-13.webp";
import project21 from "/images/projects/details/pd-21.webp";
import project22 from "/images/projects/details/pd-22.webp";
import project23 from "/images/projects/details/pd-23.webp";
import project31 from "/images/projects/details/pd-31.webp";
import project32 from "/images/projects/details/pd-32.webp";
import project33 from "/images/projects/details/pd-33.webp";
import project41 from "/images/projects/details/pd-41.webp";
import project42 from "/images/projects/details/pd-42.webp";
import project43 from "/images/projects/details/pd-43.webp";
import project51 from "/images/projects/details/pd-51.webp";
import project52 from "/images/projects/details/pd-52.webp";
import project53 from "/images/projects/details/pd-53.webp";
import project61 from "/images/projects/details/pd-61.webp";
import project62 from "/images/projects/details/pd-62.webp";

import { uniqueId } from "lodash";

import type { ProjectsTypes } from "@/types/projects/ProjectsType";

const chance = new Chance();

const ProjectGridData: ProjectsTypes[] = [
  {
    id: chance.integer({ min: 1, max: 2000 }),
    project_title: "Kente Elegance Collection",
    project_image: project1,
    tag1: "Traditional Weaving",
    tag2: "Ceremonial Wear",
    industry: "Fashion",
    raised: "₵2,500",
    description:
      "<p>The Kente Elegance Collection showcases the masterful artistry of traditional Ghanaian weaving. Each piece is handcrafted by skilled artisans from Northern Ghana, featuring intricate patterns that tell stories of heritage, wisdom, and cultural pride. These stunning garments are perfect for special occasions and celebrations.<p>",
    detail_img_1: project11,
    detail_img_2: project12,
    detail_img_3: project13,
  },
  {
    id: chance.integer({ min: 1, max: 2000 }),
    project_title: "Smock & Batakari Collection",
    project_image: project2,
    tag1: "Traditional Garments",
    tag2: "Everyday Wear",
    industry: "Fashion",
    raised: "₵180",
    description:
      "<p>The Smock & Batakari Collection celebrates the iconic traditional garments of Northern Ghana. These comfortable, flowing garments are perfect for both casual and formal occasions. Each piece is carefully crafted by local artisans using time-honored techniques passed down through generations.</p> <h4>Features of our Smock & Batakari pieces:</h4> <p>Made from high-quality locally-sourced cotton, these garments offer breathability and comfort in Ghana's warm climate. The intricate embroidery and unique patterns make each piece a work of art that tells the story of Northern Ghanaian culture.</p>",
    detail_img_1: project31,
    detail_img_2: project32,
    detail_img_3: project33,
  },
  {
    id: chance.integer({ min: 1, max: 2000 }),
    project_title: "Handwoven Accessories",
    project_image: project3,
    tag1: "Bags & Purses",
    tag2: "Jewelry",
    industry: "Fashion",
    raised: "₵95",
    description:
      "<h4>Authentic Handwoven Accessories:</h4> <p>Our collection of handwoven accessories showcases the incredible skill of Northern Ghanaian artisans. From intricately woven bags to beautiful jewelry pieces, each item is a testament to traditional craftsmanship.</p> <h5>Sustainable Materials:</h5> <p>All accessories are made using locally-sourced, sustainable materials including natural fibers, beads, and metals. Each piece supports environmental conservation while preserving traditional techniques.</p> <h5>Unique Designs:</h5> <p>Every accessory features unique patterns and designs that reflect the rich cultural heritage of Northern Ghana. No two pieces are exactly alike, making each purchase truly special.</p> <h5>Artisan Stories:</h5> <p>Each accessory comes with the story of its creator, connecting you directly to the talented artisan who crafted your piece with love and skill.</p>",
    detail_img_1: project21,
    detail_img_2: project22,
    detail_img_3: project23,
  },
  {
    id: chance.integer({ min: 1, max: 2000 }),
    project_title: "Contemporary Fusion Wear",
    project_image: project4,
    tag1: "Modern Design",
    tag2: "Cultural Fusion",
    industry: "Fashion",
    raised: "₵320",
    description:
      "<p>Our Contemporary Fusion Wear collection bridges traditional Northern Ghanaian craftsmanship with modern fashion sensibilities. These pieces are perfect for the modern professional who wants to celebrate their heritage while embracing contemporary style.</p><p>Design Philosophy: Each garment combines traditional patterns and techniques with modern cuts and silhouettes, creating versatile pieces that work in both professional and casual settings.</p><p>Quality Materials: We use premium fabrics sourced both locally and internationally, ensuring comfort, durability, and style in every piece.</p><p>Versatile Styling: These fusion pieces can be dressed up for formal occasions or styled casually for everyday wear, making them perfect additions to any wardrobe.</p>",
    detail_img_1: project61,
    detail_img_2: project62,
    detail_img_3: project43,
  },

  {
    id: chance.integer({ min: 1, max: 2000 }),
    project_title: "Artisan Footwear Collection",
    project_image: project5,
    tag1: "Handcrafted",
    tag2: "Leather Work",
    industry: "Fashion",
    raised: "₵250",
    description:
      "<p>Our Artisan Footwear Collection showcases the exceptional leatherworking skills of Northern Ghanaian craftsmen. Each pair of shoes is meticulously handcrafted using traditional techniques passed down through generations.</p>    <h4>1. Traditional Sandals</h4>    <p>Our signature sandals feature intricate leather work and comfortable designs perfect for Ghana's climate. Each pair is made from locally-sourced leather and decorated with traditional patterns.</p>    <h4>2. Modern Leather Shoes</h4>    <p>Combining traditional craftsmanship with contemporary design, these shoes are perfect for both formal and casual occasions. The attention to detail and quality construction ensures durability and comfort.</p>    <h4>3. Custom Designs</h4>    <p>We offer custom footwear services where customers can work directly with our artisans to create unique pieces. <strong>Starting at ₵350</strong>, these bespoke shoes are tailored to individual preferences while maintaining authentic Ghanaian craftsmanship.</p>",
    detail_img_1: project41,
    detail_img_2: project42,
    detail_img_3: project43,
  },

  {
    id: chance.integer({ min: 1, max: 2000 }),
    project_title: "Traditional Textile Art",
    project_image: project6,
    tag1: "Handwoven Fabrics",
    tag2: "Cultural Heritage",
    industry: "Fashion",
    raised: "₵450",
    description:
      "<p class='text-subtitle-1'>Our Traditional Textile Art collection showcases the rich weaving heritage of Northern Ghana. Each piece represents hours of meticulous handwork by master weavers who have preserved these ancient techniques for generations.</p><h4 class='text-h4'>1. Authentic Kente Strips</h4><p class='text-subtitle-1'>These narrow strips of handwoven Kente cloth feature traditional patterns and vibrant colors. Each strip tells a story and can be used to create larger garments or displayed as art pieces.</p><h4 class='text-h4'>2. Ceremonial Fabrics</h4><p class='text-subtitle-1'>Our ceremonial fabrics are woven specifically for special occasions and traditional ceremonies. These pieces feature intricate patterns that have been passed down through generations, each with its own cultural significance and meaning.</p>",
    detail_img_1: project51,
    detail_img_2: project52,
    detail_img_3: project53,
  },
];

mock.onGet("/api/data/projects/grid").reply(() => {
  return [200, ProjectGridData];
});

// ----------------------------------------------------------------------
mock.onPost("/api/data/projects/post").reply((config: string | any) => {
  try {
    const { title } = JSON.parse(config.data);

    const paramCase = (t: string) =>
      t
        .toLowerCase()
        .replace(/ /g, "-")
        .replace(/[^\w-]+/g, "");

    const post = ProjectGridData.find(
      (_post: ProjectsTypes | string | any) =>
        paramCase(_post.project_title) === title
    );

    if (!post) {
      return [404, { message: "Post not found" }];
    }

    return [200, { post }];
  } catch (error) {
    console.error(error);
    return [500, { message: "Internal server error" }];
  }
});
